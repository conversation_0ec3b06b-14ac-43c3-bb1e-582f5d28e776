defmodule MqttableWeb.DemoAdvancedToolsLive do
  @moduledoc """
  Demo page for testing the Advanced Tools functionality in the Enhanced Payload Editor.
  """

  use MqttableWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "Advanced Tools Demo")
      |> assign(:payload, "")
      |> assign(:payload_format, "text")

    {:ok, socket}
  end

  @impl true
  def handle_info({:payload_editor_changed, payload, format}, socket) do
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:payload_format, format)

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-base-content mb-2">🔧 Advanced Tools Demo</h1>
          <p class="text-base-content/70">
            Test the new Advanced Tools functionality with Data Processing, Text Processing, and Math Functions.
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Enhanced Payload Editor -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <h2 class="card-title">Enhanced Payload Editor</h2>
              <p class="text-sm text-base-content/70 mb-4">
                Select "🔧 Advanced Tools" from the category dropdown to access the new functions.
              </p>

              <.live_component
                module={MqttableWeb.EnhancedPayloadEditorComponent}
                id="demo-payload-editor"
                payload={@payload}
                payload_format={@payload_format}
                label="Test Payload"
              />
            </div>
          </div>
          
    <!-- Example Templates -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <h2 class="card-title">Example Templates</h2>
              <p class="text-sm text-base-content/70 mb-4">
                Click on any example to copy it to the editor.
              </p>

              <div class="space-y-4">
                <!-- Data Processing Examples -->
                <div class="collapse collapse-arrow bg-base-200">
                  <input type="checkbox" />
                  <div class="collapse-title text-sm font-medium">📊 Data Processing Examples</div>
                  <div class="collapse-content">
                    <div class="space-y-2">
                      <button
                        class="btn btn-sm btn-outline w-full text-left justify-start"
                        phx-click={
                          JS.dispatch("copy-template", detail: %{template: data_processing_example()})
                        }
                      >
                        Base64 & JSON Processing
                      </button>
                      <button
                        class="btn btn-sm btn-outline w-full text-left justify-start"
                        phx-click={JS.dispatch("copy-template", detail: %{template: hash_example()})}
                      >
                        Hash & URL Encoding
                      </button>
                    </div>
                  </div>
                </div>
                
    <!-- Text Processing Examples -->
                <div class="collapse collapse-arrow bg-base-200">
                  <input type="checkbox" />
                  <div class="collapse-title text-sm font-medium">📝 Text Processing Examples</div>
                  <div class="collapse-content">
                    <div class="space-y-2">
                      <button
                        class="btn btn-sm btn-outline w-full text-left justify-start"
                        phx-click={
                          JS.dispatch("copy-template", detail: %{template: text_formatting_example()})
                        }
                      >
                        Text Formatting
                      </button>
                      <button
                        class="btn btn-sm btn-outline w-full text-left justify-start"
                        phx-click={
                          JS.dispatch("copy-template", detail: %{template: padding_example()})
                        }
                      >
                        Text Padding
                      </button>
                    </div>
                  </div>
                </div>
                
    <!-- Math Functions Examples -->
                <div class="collapse collapse-arrow bg-base-200">
                  <input type="checkbox" />
                  <div class="collapse-title text-sm font-medium">🧮 Math Functions Examples</div>
                  <div class="collapse-content">
                    <div class="space-y-2">
                      <button
                        class="btn btn-sm btn-outline w-full text-left justify-start"
                        phx-click={JS.dispatch("copy-template", detail: %{template: math_example()})}
                      >
                        Math Operations
                      </button>
                      <button
                        class="btn btn-sm btn-outline w-full text-left justify-start"
                        phx-click={
                          JS.dispatch("copy-template", detail: %{template: sensor_math_example()})
                        }
                      >
                        Sensor Data with Math
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
    <!-- Current Payload Display -->
        <div class="card bg-base-100 shadow-xl mt-8">
          <div class="card-body">
            <h2 class="card-title">Current Payload</h2>
            <div class="mockup-code">
              <pre><code><%= @payload %></code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener('copy-template', function(event) {
        const template = event.detail.template;
        const textarea = document.querySelector('#payload-editor-demo-payload-editor');
        if (textarea) {
          textarea.value = template;
          textarea.dispatchEvent(new Event('input', { bubbles: true }));
          textarea.focus();
        }
      });
    </script>
    """
  end

  # Example template functions
  defp data_processing_example do
    """
    {
      "user_data": "{{ name | base64_encode }}",
      "config": {{ %{device: device_id, temp: temperature} | json_encode }},
      "checksum": "{{ device_id | hash }}",
      "encoded_url": "{{ \"https://api.example.com/device/\" | url_encode }}"
    }
    """
  end

  defp hash_example do
    """
    {
      "device_id": "{{ device_id }}",
      "signature": "{{ device_id | hash: \"sha256\" }}",
      "encoded_name": "{{ name | url_encode }}",
      "data_b64": "{{ email | base64_encode }}"
    }
    """
  end

  defp text_formatting_example do
    """
    {
      "title": "{{ name | uppercase }}",
      "description": "{{ sentence | capitalize }}",
      "short_desc": "{{ sentence | truncate: 20 }}",
      "formatted_name": "{{ first_name | lowercase }}"
    }
    """
  end

  defp padding_example do
    """
    {
      "device_id": "{{ device_id | pad_left: 12, \"0\" }}",
      "status_code": "{{ device_status | pad_right: 10, \"-\" }}",
      "formatted_temp": "{{ temperature | pad_left: 6, \" \" }}°C"
    }
    """
  end

  defp math_example do
    """
    {
      "temperature": {{ temperature }},
      "temp_rounded": {{ temperature | round: 1 }},
      "temp_ceiling": {{ temperature | ceil }},
      "temp_floor": {{ temperature | floor }},
      "abs_signal": {{ signal_strength | abs }}
    }
    """
  end

  defp sensor_math_example do
    """
    {
      "device": "{{ device_id }}",
      "readings": {
        "temperature": {{ temperature | round: 2 }},
        "humidity": {{ humidity | ceil }},
        "pressure": {{ pressure | floor }},
        "battery_pct": {{ battery_level | clamp: 0, 100 }}
      },
      "metadata": {
        "signal_strength": {{ signal_strength | abs }},
        "uptime_hours": {{ uptime | max: 0 }}
      }
    }
    """
  end
end
