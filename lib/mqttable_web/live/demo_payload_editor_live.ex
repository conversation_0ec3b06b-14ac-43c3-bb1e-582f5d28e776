defmodule MqttableWeb.DemoPayloadEditorLive do
  @moduledoc """
  Demo page for the new simplified payload editor.
  This shows the before/after comparison and demonstrates the new features.
  """

  use MqttableWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "Payload Editor Demo")
      |> assign(:demo_form, default_demo_form())
      |> assign(:show_comparison, false)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="container mx-auto px-4 py-8 max-w-6xl"
      id="payload-editor-demo"
      phx-hook="PayloadEditor"
    >
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-base-content mb-4">
          🎯 New Simplified Payload Editor
        </h1>
        <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
          A unified, intelligent editor that supports both plain text and template syntax
          without complex mode switching.
        </p>
      </div>
      
    <!-- Key Features -->
      <div class="grid md:grid-cols-3 gap-6 mb-12">
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body text-center">
            <div class="text-3xl mb-3">🔄</div>
            <h3 class="card-title justify-center">Unified Interface</h3>
            <p class="text-sm text-base-content/70">
              Single textarea for all input types. No more mode switching between template and manual.
            </p>
          </div>
        </div>

        <div class="card bg-base-100 shadow-lg">
          <div class="card-body text-center">
            <div class="text-3xl mb-3">👁️</div>
            <h3 class="card-title justify-center">Live Preview</h3>
            <p class="text-sm text-base-content/70">
              Real-time rendering preview when template syntax is detected automatically.
            </p>
          </div>
        </div>

        <div class="card bg-base-100 shadow-lg">
          <div class="card-body text-center">
            <div class="text-3xl mb-3">🛠️</div>
            <h3 class="card-title justify-center">Template Helper</h3>
            <p class="text-sm text-base-content/70">
              Quick insert buttons and examples to help you build templates easily.
            </p>
          </div>
        </div>
      </div>
      
    <!-- Demo Section -->
      <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body">
          <h2 class="card-title text-2xl mb-6">
            <.icon name="hero-play" class="size-6" /> Try the New Editor
          </h2>

          <div class="space-y-6">
            <!-- New Payload Editor with Template Support -->
            <.live_component
              module={MqttableWeb.PayloadEditorComponent}
              id="demo-payload-editor"
              payload={@demo_form["payload"] || ""}
              payload_format={@demo_form["payload_format"] || "text"}
              label="Payload Content"
            />
            
    <!-- Example Templates -->
            <div class="bg-base-200 rounded-lg p-4">
              <h3 class="font-semibold mb-3">📝 Try These Examples:</h3>
              <div class="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 class="font-medium text-sm mb-2">Simple Sensor Data:</h4>
                  <pre class="text-xs bg-base-300 p-2 rounded block whitespace-pre-wrap">Temperature: &#123;&#123; temperature &#125;&#125;°C
    Device: &#123;&#123; device_id &#125;&#125;
    Time: &#123;&#123; iso8601 &#125;&#125;</pre>
                </div>
                <div>
                  <h4 class="font-medium text-sm mb-2">JSON Format:</h4>
                  <pre class="text-xs bg-base-300 p-2 rounded block whitespace-pre-wrap">&#123;
    "temp": &#123;&#123; 20 | temperature: 30 &#125;&#125;,
    "id": "&#123;&#123; device_id &#125;&#125;",
    "ts": "&#123;&#123; iso8601 &#125;&#125;"
    &#125;</pre>
                </div>
              </div>
            </div>
            
    <!-- Current Form State -->
            <div class="bg-info/10 rounded-lg p-4">
              <h3 class="font-semibold mb-2">📊 Current Form State:</h3>
              <div class="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Payload Format:</strong> {@demo_form["payload_format"]}
                </div>
                <div>
                  <strong>Content Length:</strong> {String.length(@demo_form["payload"] || "")} chars
                </div>
                <div class="md:col-span-2">
                  <strong>Has Template Syntax:</strong>
                  {if has_template_syntax?(@demo_form["payload"]), do: "✅ Yes", else: "❌ No"}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
    <!-- Comparison Section -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex items-center justify-between mb-6">
            <h2 class="card-title text-2xl">
              <.icon name="hero-scale" class="size-6" /> Before vs After Comparison
            </h2>
            <button class="btn btn-outline btn-sm" phx-click="toggle_comparison">
              {if @show_comparison, do: "Hide", else: "Show"} Comparison
            </button>
          </div>

          <%= if @show_comparison do %>
            <div class="grid md:grid-cols-2 gap-6">
              <!-- Before (Old Complex System) -->
              <div class="border border-error/30 rounded-lg p-4">
                <h3 class="font-semibold text-error mb-3">❌ Before (Complex)</h3>
                <ul class="space-y-2 text-sm">
                  <li>• Separate template selector component</li>
                  <li>• Mode switching between template/manual</li>
                  <li>• Complex state synchronization</li>
                  <li>• Template variables in separate inputs</li>
                  <li>• Preview only in template mode</li>
                  <li>• Multiple form fields to manage</li>
                  <li>• Confusing user experience</li>
                </ul>
              </div>
              
    <!-- After (New Simplified System) -->
              <div class="border border-success/30 rounded-lg p-4">
                <h3 class="font-semibold text-success mb-3">✅ After (Simplified)</h3>
                <ul class="space-y-2 text-sm">
                  <li>• Single unified textarea</li>
                  <li>• Automatic template detection</li>
                  <li>• Real-time preview when needed</li>
                  <li>• Template helper for assistance</li>
                  <li>• No mode switching required</li>
                  <li>• Minimal state management</li>
                  <li>• Intuitive user experience</li>
                </ul>
              </div>
            </div>

            <div class="mt-6 bg-success/10 rounded-lg p-4">
              <h3 class="font-semibold text-success mb-2">🎉 Benefits:</h3>
              <div class="grid md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong>Reduced Complexity:</strong> <br /> ~70% less code to maintain
                </div>
                <div>
                  <strong>Better UX:</strong> <br /> No learning curve for modes
                </div>
                <div>
                  <strong>More Flexible:</strong> <br /> Mix plain text and templates freely
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
      
    <!-- Implementation Notes -->
      <div class="mt-8 bg-base-200 rounded-lg p-6">
        <h3 class="font-semibold mb-4">🔧 Implementation Notes:</h3>
        <div class="grid md:grid-cols-2 gap-6 text-sm">
          <div>
            <h4 class="font-medium mb-2">New Components:</h4>
            <ul class="space-y-1">
              <li>• <code>PayloadEditorComponent</code></li>
              <li>• <code>SimplifiedMessageForm</code></li>
              <li>• JavaScript cursor insertion</li>
              <li>• CSS syntax highlighting</li>
            </ul>
          </div>
          <div>
            <h4 class="font-medium mb-2">Removed Complexity:</h4>
            <ul class="space-y-1">
              <li>• Template selector component</li>
              <li>• Template variables management</li>
              <li>• Mode switching logic</li>
              <li>• Complex state synchronization</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("toggle_comparison", _params, socket) do
    {:noreply, assign(socket, :show_comparison, !socket.assigns.show_comparison)}
  end

  @impl true
  def handle_info({:payload_editor_changed, payload, payload_format}, socket) do
    updated_form =
      socket.assigns.demo_form
      |> Map.put("payload", payload)
      |> Map.put("payload_format", payload_format)

    {:noreply, assign(socket, :demo_form, updated_form)}
  end

  # Helper Functions

  defp default_demo_form do
    %{
      "payload" => "Hello {{ device_id }}, temperature is {{ temperature }}°C at {{ iso8601 }}",
      "payload_format" => "text"
    }
  end

  defp has_template_syntax?(content) when is_binary(content) do
    String.contains?(content, "{{") || String.contains?(content, "{%")
  end

  defp has_template_syntax?(_), do: false
end
