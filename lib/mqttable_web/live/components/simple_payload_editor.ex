defmodule MqttableWeb.SimplePayloadEditor do
  @moduledoc """
  A simple payload editor using pure DaisyUI components to avoid input issues.
  """

  use MqttableWeb, :live_component
  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:payload, fn -> "" end)
      |> assign_new(:payload_format, fn -> "text" end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    payload = Map.get(assigns, :payload, socket.assigns[:payload] || "")
    payload_format = Map.get(assigns, :payload_format, socket.assigns[:payload_format] || "text")

    socket =
      socket
      |> assign(assigns)
      |> assign(:payload, payload)
      |> assign(:payload_format, payload_format)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="form-control w-full">
      <label class="label">
        <span class="label-text font-medium">{Map.get(assigns, :label, "Payload")}</span>
      </label>
      
    <!-- Simple DaisyUI Textarea -->
      <textarea
        name="payload"
        placeholder={get_placeholder(@payload_format)}
        class="textarea textarea-bordered w-full h-32"
      ><%= @payload %></textarea>
      
    <!-- Format Selection -->
      <div class="mt-3">
        <span class="label-text text-sm">Format:</span>
        <div class="join ml-2">
          <input
            class="join-item btn btn-xs"
            type="radio"
            name={"format-#{@myself}"}
            aria-label="Text"
            checked={@payload_format == "text"}
            phx-click="format_changed"
            phx-value-format="text"
            phx-target={@myself}
          />
          <input
            class="join-item btn btn-xs"
            type="radio"
            name={"format-#{@myself}"}
            aria-label="JSON"
            checked={@payload_format == "json"}
            phx-click="format_changed"
            phx-value-format="json"
            phx-target={@myself}
          />
          <input
            class="join-item btn btn-xs"
            type="radio"
            name={"format-#{@myself}"}
            aria-label="Hex"
            checked={@payload_format == "hex"}
            phx-click="format_changed"
            phx-value-format="hex"
            phx-target={@myself}
          />
        </div>
      </div>
      
    <!-- Hidden inputs for form submission -->
      <input type="hidden" name="payload_format" value={@payload_format} />
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket = assign(socket, :payload_format, format)

    # Notify parent component
    send(self(), {:payload_editor_changed, socket.assigns.payload, format})

    {:noreply, socket}
  end

  # Helper Functions

  defp get_placeholder(format) do
    case format do
      "json" -> ~s({"message": "Hello World", "value": 123})
      "hex" -> "48656C6C6F20576F726C64"
      _ -> "Hello World"
    end
  end
end
