defmodule MqttableWeb.SidebarTemplateHelperComponent do
  @moduledoc """
  Sidebar template helper component for the send message modal.

  This component provides template functionality in a sidebar layout:
  - Category filtering
  - Function insertion
  - Live preview
  - Example templates
  """

  use MqttableWeb, :live_component
  alias Mqttable.Templating.Engine
  alias MqttableWeb.Live.Components.ArrayDataExample
  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:selected_category, fn -> "all" end)
      |> assign_new(:show_all_functions, fn -> false end)
      |> assign_new(:advanced_groups_expanded, fn -> %{} end)
      |> assign_new(:preview_result, fn -> {:ok, ""} end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # Get payload and format from assigns
    payload = Map.get(assigns, :payload, "")
    _payload_format = Map.get(assigns, :payload_format, "text")
    active_broker_name = Map.get(assigns, :active_broker_name)

    # Update preview
    preview_result = generate_preview(payload, active_broker_name)

    socket =
      socket
      |> assign(assigns)
      |> assign(:preview_result, preview_result)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="h-full flex flex-col">
      <!-- Template Helper Header -->
      <div class="flex-shrink-0 mb-4">
        <h4 class="text-md font-semibold flex items-center mb-3">
          <.icon name="hero-sparkles" class="size-4 mr-2" /> Template Helper
        </h4>
        
    <!-- Category Filter -->
        <select
          name="category"
          class="select select-sm select-bordered w-full"
          phx-change="filter_category"
          phx-target={@myself}
        >
          <option value="all" selected={@selected_category == "all"}>All Categories</option>
          <option value="iot" selected={@selected_category == "iot"}>IoT & Sensors</option>
          <option value="person" selected={@selected_category == "person"}>Person Data</option>
          <option value="address" selected={@selected_category == "address"}>
            Address & Location
          </option>
          <option value="company" selected={@selected_category == "company"}>
            Company & Business
          </option>
          <option value="internet" selected={@selected_category == "internet"}>
            Internet & Tech
          </option>
          <option value="commerce" selected={@selected_category == "commerce"}>
            Commerce & Products
          </option>
          <option value="text" selected={@selected_category == "text"}>Text & Content</option>
          <option value="advanced" selected={@selected_category == "advanced"}>
            🔧 Advanced Tools
          </option>
          <option value="other" selected={@selected_category == "other"}>Other</option>
        </select>
      </div>
      
    <!-- Quick Insert Functions -->
      <div class="flex-1 overflow-y-auto">
        <div class="mb-4">
          <div class="flex items-center justify-between mb-2">
            <div class="text-sm font-medium">Quick Insert:</div>
            <%= if not @show_all_functions do %>
              <button
                type="button"
                class="btn btn-xs btn-ghost"
                phx-click="toggle_show_all"
                phx-target={@myself}
              >
                Show More ({length(get_filtered_functions(@selected_category)) -
                  length(get_common_functions())})
              </button>
            <% else %>
              <button
                type="button"
                class="btn btn-xs btn-ghost"
                phx-click="toggle_show_all"
                phx-target={@myself}
              >
                Show Less
              </button>
            <% end %>
          </div>

          <%= if @selected_category == "advanced" do %>
            <!-- Advanced Tools with Accordion Groups -->
            <div class="space-y-2">
              <%= for {group_key, group_info} <- get_advanced_groups() do %>
                <div class="collapse collapse-arrow bg-base-100 border border-base-300">
                  <input
                    type="checkbox"
                    checked={Map.get(@advanced_groups_expanded, group_key, false)}
                    phx-click="toggle_advanced_group"
                    phx-value-group={group_key}
                    phx-target={@myself}
                  />
                  <div class="collapse-title text-xs font-medium py-2">
                    {group_info.icon} {group_info.title} ({length(group_info.functions)})
                  </div>
                  <div class="collapse-content">
                    <div class="flex flex-wrap gap-1 pt-1">
                      <%= for function_info <- group_info.functions do %>
                        <button
                          type="button"
                          class="btn btn-xs btn-outline"
                          phx-click="insert_template"
                          phx-value-template={"{{ #{function_info.name} }}"}
                          phx-target={@myself}
                          title={function_info.description}
                        >
                          {function_info.icon} {function_info.display_name}
                        </button>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <!-- Regular Function Display -->
            <div class="flex flex-wrap gap-1">
              <%= for function_info <- get_displayed_functions(@selected_category, @show_all_functions) do %>
                <button
                  type="button"
                  class="btn btn-xs btn-outline"
                  phx-click="insert_template"
                  phx-value-template={"{{ #{function_info.name} }}"}
                  phx-target={@myself}
                  title={function_info.description}
                >
                  {function_info.icon} {function_info.display_name}
                </button>
              <% end %>
            </div>
          <% end %>
        </div>
        
    <!-- Example Templates -->
        <div class="mb-4">
          <div class="text-sm font-medium mb-2">Example Templates:</div>
          
    <!-- Basic Examples -->
          <div class="grid grid-cols-1 gap-1 mb-2">
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={sensor_data_example()}
              phx-target={@myself}
            >
              📊 IoT Sensor
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={user_profile_example()}
              phx-target={@myself}
            >
              👤 User Profile
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={device_status_example()}
              phx-target={@myself}
            >
              🔧 Device Status
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.for_loop_example()}
              phx-target={@myself}
            >
              🔄 For Loop
            </button>
          </div>
          
    <!-- Advanced Examples -->
          <div class="text-xs font-medium mb-1 text-base-content/70">Advanced Liquid:</div>
          <div class="grid grid-cols-1 gap-1 mb-2">
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.for_loop_array_example()}
              phx-target={@myself}
            >
              🔄 Array Loop
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.nested_for_loop_example()}
              phx-target={@myself}
            >
              🔄 Nested Loop
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.conditional_for_loop_example()}
              phx-target={@myself}
            >
              🔄 Conditional Loop
            </button>
          </div>
          
    <!-- Liquid Documentation Link -->
          <div class="text-xs text-base-content/60 mt-2 pt-2 border-t border-base-300">
            💡 Supports full
            <a href="https://shopify.github.io/liquid/" target="_blank" class="link link-primary">
              Liquid syntax
            </a>
          </div>
        </div>
      </div>
      
    <!-- Live Preview at Bottom -->
      <%= if String.contains?(@payload, "{{") || String.contains?(@payload, "{%") do %>
        <div class="flex-shrink-0 mt-4 pt-4 border-t border-base-300">
          <div class="text-sm font-medium mb-2 flex items-center gap-2">
            <.icon name="hero-eye" class="size-4" /> Live Preview:
          </div>
          <div class="bg-base-100 border border-base-300 rounded-lg p-3 text-xs font-mono max-h-32 overflow-y-auto">
            <%= case @preview_result do %>
              <% {:ok, result} -> %>
                <pre class="whitespace-pre-wrap text-success"><%= result %></pre>
              <% {:error, error} -> %>
                <div class="text-error">Error: {error}</div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("filter_category", %{"value" => category}, socket) do
    {:noreply, assign(socket, :selected_category, category)}
  end

  @impl true
  def handle_event("toggle_show_all", _params, socket) do
    {:noreply, assign(socket, :show_all_functions, !socket.assigns.show_all_functions)}
  end

  @impl true
  def handle_event("toggle_advanced_group", %{"group" => group_key}, socket) do
    current_expanded = socket.assigns.advanced_groups_expanded

    new_expanded =
      Map.put(current_expanded, group_key, !Map.get(current_expanded, group_key, false))

    {:noreply, assign(socket, :advanced_groups_expanded, new_expanded)}
  end

  @impl true
  def handle_event("insert_template", %{"template" => template}, socket) do
    # Push event to JavaScript to insert at cursor position in the target textarea
    # Also notify parent about the payload change
    new_payload = socket.assigns.payload <> template
    send(self(), {:payload_editor_changed, new_payload, socket.assigns.payload_format})

    {:noreply,
     push_event(socket, "insert_at_cursor", %{
       target_id: socket.assigns.target_textarea_id,
       text: template
     })}
  end

  # Helper Functions (reuse from EnhancedPayloadEditorComponent)

  defp generate_preview(payload, active_broker_name) do
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(active_broker_name)

      case Engine.render(payload, %{}, variables) do
        {:ok, result} -> {:ok, result}
        {:error, error} -> {:error, inspect(error)}
      end
    else
      {:ok, payload}
    end
  end

  defp get_filtered_functions(category) do
    all_functions = get_all_functions()
    filter_by_category(all_functions, category)
  end

  defp get_common_functions do
    # Most commonly used functions (displayed by default)
    [
      "temperature",
      "humidity",
      "device_id",
      "iso8601",
      "uuid",
      "name",
      "email",
      "city",
      "country",
      "ipv4",
      "product_name"
    ]
  end

  defp get_displayed_functions(category, show_all) do
    filtered = get_filtered_functions(category)

    if show_all do
      filtered
    else
      common_names = get_common_functions() |> MapSet.new()
      Enum.filter(filtered, fn func -> func.name in common_names end)
    end
  end

  defp get_advanced_groups do
    %{
      "data_processing" => %{
        title: "Data Processing",
        icon: "📊",
        functions: [
          %{
            name: "base64_encode",
            display_name: "Base64 Encode",
            icon: "🔐",
            description: "Encode to Base64"
          },
          %{
            name: "base64_decode",
            display_name: "Base64 Decode",
            icon: "🔓",
            description: "Decode from Base64"
          },
          %{
            name: "json_encode",
            display_name: "JSON Encode",
            icon: "📝",
            description: "Encode to JSON"
          },
          %{name: "hash", display_name: "Hash", icon: "🔒", description: "Generate hash"}
        ]
      },
      "math" => %{
        title: "Math Functions",
        icon: "🧮",
        functions: [
          %{name: "round", display_name: "Round", icon: "🔢", description: "Round number"},
          %{name: "ceil", display_name: "Ceiling", icon: "⬆️", description: "Round up"},
          %{name: "floor", display_name: "Floor", icon: "⬇️", description: "Round down"},
          %{
            name: "random_int",
            display_name: "Random Int",
            icon: "🎲",
            description: "Random integer"
          }
        ]
      }
    }
  end

  defp filter_by_category(functions, category) do
    case category do
      "all" -> functions
      _ -> Enum.filter(functions, fn func -> func.category == category end)
    end
  end

  defp get_all_functions do
    [
      # IoT & Sensors
      %{
        name: "temperature",
        display_name: "Temperature",
        icon: "🌡️",
        category: "iot",
        description: "Random temperature"
      },
      %{
        name: "humidity",
        display_name: "Humidity",
        icon: "💧",
        category: "iot",
        description: "Random humidity"
      },
      %{
        name: "pressure",
        display_name: "Pressure",
        icon: "📊",
        category: "iot",
        description: "Random pressure"
      },
      %{
        name: "device_id",
        display_name: "Device ID",
        icon: "📱",
        category: "iot",
        description: "Random device ID"
      },
      %{
        name: "device_status",
        display_name: "Device Status",
        icon: "🔋",
        category: "iot",
        description: "Random device status"
      },

      # Person Data
      %{
        name: "name",
        display_name: "Name",
        icon: "👤",
        category: "person",
        description: "Random full name"
      },
      %{
        name: "first_name",
        display_name: "First Name",
        icon: "👤",
        category: "person",
        description: "Random first name"
      },
      %{
        name: "last_name",
        display_name: "Last Name",
        icon: "👤",
        category: "person",
        description: "Random last name"
      },
      %{
        name: "email",
        display_name: "Email",
        icon: "📧",
        category: "person",
        description: "Random email address"
      },

      # Address & Location
      %{
        name: "city",
        display_name: "City",
        icon: "🏙️",
        category: "address",
        description: "Random city name"
      },
      %{
        name: "country",
        display_name: "Country",
        icon: "🌍",
        category: "address",
        description: "Random country name"
      },
      %{
        name: "address",
        display_name: "Address",
        icon: "🏠",
        category: "address",
        description: "Random street address"
      },
      %{
        name: "latitude",
        display_name: "Latitude",
        icon: "🗺️",
        category: "address",
        description: "Random latitude"
      },
      %{
        name: "longitude",
        display_name: "Longitude",
        icon: "🗺️",
        category: "address",
        description: "Random longitude"
      },

      # Company & Business
      %{
        name: "company",
        display_name: "Company",
        icon: "🏢",
        category: "company",
        description: "Random company name"
      },
      %{
        name: "buzzword",
        display_name: "Buzzword",
        icon: "💼",
        category: "company",
        description: "Random business buzzword"
      },

      # Internet & Tech
      %{
        name: "ipv4",
        display_name: "IPv4",
        icon: "🌐",
        category: "internet",
        description: "Random IPv4 address"
      },
      %{
        name: "ipv6",
        display_name: "IPv6",
        icon: "🌐",
        category: "internet",
        description: "Random IPv6 address"
      },
      %{
        name: "url",
        display_name: "URL",
        icon: "🔗",
        category: "internet",
        description: "Random URL"
      },
      %{
        name: "domain",
        display_name: "Domain",
        icon: "🌐",
        category: "internet",
        description: "Random domain name"
      },

      # Commerce & Products
      %{
        name: "product_name",
        display_name: "Product Name",
        icon: "📦",
        category: "commerce",
        description: "Random product name"
      },
      %{
        name: "price",
        display_name: "Price",
        icon: "💰",
        category: "commerce",
        description: "Random price"
      },
      %{
        name: "color_name",
        display_name: "Color",
        icon: "🎨",
        category: "commerce",
        description: "Random color name"
      },

      # Text & Content
      %{
        name: "sentence",
        display_name: "Sentence",
        icon: "📝",
        category: "text",
        description: "Random sentence"
      },
      %{
        name: "paragraph",
        display_name: "Paragraph",
        icon: "📄",
        category: "text",
        description: "Random paragraph"
      },
      %{
        name: "word",
        display_name: "Word",
        icon: "🔤",
        category: "text",
        description: "Random word"
      },

      # Time & Date
      %{
        name: "iso8601",
        display_name: "ISO8601",
        icon: "⏰",
        category: "other",
        description: "Current timestamp"
      },
      %{
        name: "unix_timestamp",
        display_name: "Unix Time",
        icon: "⏱️",
        category: "other",
        description: "Unix timestamp"
      },
      %{
        name: "now",
        display_name: "Now",
        icon: "🕐",
        category: "other",
        description: "Current time"
      },

      # Random & Utility
      %{
        name: "uuid",
        display_name: "UUID",
        icon: "🆔",
        category: "other",
        description: "Random UUID"
      },
      %{
        name: "random_int",
        display_name: "Random Int",
        icon: "🎲",
        category: "other",
        description: "Random integer"
      },
      %{
        name: "random_string",
        display_name: "Random String",
        icon: "🔤",
        category: "other",
        description: "Random string"
      }
    ]
  end

  defp sensor_data_example do
    """
    {
      "device_id": "{{ device_id }}",
      "timestamp": "{{ iso8601 }}",
      "temperature": {{ temperature }},
      "humidity": {{ humidity }},
      "location": {
        "city": "{{ city }}",
        "country": "{{ country }}"
      }
    }
    """
  end

  defp user_profile_example do
    """
    {
      "user_id": "{{ uuid }}",
      "name": "{{ name }}",
      "email": "{{ email }}",
      "username": "{{ username }}",
      "address": {
        "street": "{{ address }}",
        "city": "{{ city }}",
        "postcode": "{{ postcode }}",
        "country": "{{ country }}"
      }
    }
    """
  end

  defp device_status_example do
    """
    {
      "device": "{{ device_id }}",
      "status": "{{ device_status }}",
      "uptime": {{ uptime }},
      "last_seen": "{{ iso8601 }}",
      "metrics": {
        "temperature": {{ temperature }},
        "humidity": {{ humidity }},
        "battery": {{ battery_level }}
      }
    }
    """
  end

  defp get_broker_variables(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        %{}

      broker ->
        # Extract enabled variables and convert to map
        broker
        |> Map.get(:variables, [])
        |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
        |> Enum.reduce(%{}, fn var, acc ->
          name = Map.get(var, :name)
          value = Map.get(var, :value, "")

          if name && name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
    end
  end

  defp get_broker_variables(_broker_name) do
    %{}
  end
end
