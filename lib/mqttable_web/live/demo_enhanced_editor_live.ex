defmodule MqttableWeb.DemoEnhancedEditorLive do
  @moduledoc """
  Demo page for the enhanced payload editor component.
  """

  use MqttableWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "Enhanced Template Editor Demo")
      |> assign(:payload, "")
      |> assign(:payload_format, "text")

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    # Define example templates as module attributes to avoid HEEx parsing issues
    assigns =
      assign(
        assigns,
        :iot_example,
        ~s|{"device": "{{ device_id }}", "temp": {{ temperature }}, "humidity": {{ humidity }}}|
      )

    assigns =
      assign(
        assigns,
        :user_example,
        ~s|{"name": "{{ name }}", "email": "{{ email }}", "company": "{{ company }}"}|
      )

    assigns =
      assign(
        assigns,
        :product_example,
        ~s|Product: {{ product_name }} - ${{ price }} ({{ color_name }})|
      )

    ~H"""
    <div class="container mx-auto px-4 py-8 max-w-4xl">
      <div class="mb-8">
        <h1 class="text-3xl font-bold mb-2">Enhanced Template Editor Demo</h1>
        <p class="text-base-content/70">
          Test the new enhanced payload editor with 105+ template functions, search, categorization, and clean function names.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Editor Panel -->
        <div class="space-y-6">
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h2 class="card-title">Enhanced Payload Editor</h2>
              <p class="text-sm text-base-content/70 mb-4">
                Features: Search, Categories, Show More/Less, Clean Function Names
              </p>

              <.live_component
                module={MqttableWeb.EnhancedPayloadEditorComponent}
                id="demo-enhanced-editor"
                payload={@payload}
                payload_format={@payload_format}
                label="Template Payload"
              />
            </div>
          </div>
          
    <!-- Feature Highlights -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title text-lg">✨ New Features</h3>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start gap-2">
                  <span class="text-success">🔍</span>
                  <span><strong>Search Functions:</strong> Type to find functions instantly</span>
                </li>
                <li class="flex items-start gap-2">
                  <span class="text-info">📂</span>
                  <span>
                    <strong>Categories:</strong> Filter by IoT, Person, Address, Company, etc.
                  </span>
                </li>
                <li class="flex items-start gap-2">
                  <span class="text-warning">📋</span>
                  <span>
                    <strong>Show More/Less:</strong>
                    Start with 12 common functions, expand to see all 105+
                  </span>
                </li>
                <li class="flex items-start gap-2">
                  <span class="text-primary">✨</span>
                  <span>
                    <strong>Clean Names:</strong>
                    Use <code>&#123;&#123;name&#125;&#125;</code>
                    instead of <code>&#123;&#123;fake_name&#125;&#125;</code>
                  </span>
                </li>
                <li class="flex items-start gap-2">
                  <span class="text-secondary">👁️</span>
                  <span><strong>Live Preview:</strong> See template results in real-time</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
    <!-- Examples and Documentation -->
        <div class="space-y-6">
          <!-- Quick Examples -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title text-lg">📝 Quick Examples</h3>
              <div class="space-y-3">
                <div>
                  <h4 class="font-semibold text-sm mb-1">IoT Sensor Data:</h4>
                  <pre class="text-xs bg-base-200 p-2 rounded block overflow-x-auto"><%= @iot_example %></pre>
                </div>

                <div>
                  <h4 class="font-semibold text-sm mb-1">User Profile:</h4>
                  <pre class="text-xs bg-base-200 p-2 rounded block overflow-x-auto"><%= @user_example %></pre>
                </div>

                <div>
                  <h4 class="font-semibold text-sm mb-1">Product Info:</h4>
                  <pre class="text-xs bg-base-200 p-2 rounded block overflow-x-auto"><%= @product_example %></pre>
                </div>
              </div>
            </div>
          </div>
          
    <!-- Function Categories -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title text-lg">📂 Function Categories</h3>
              <div class="grid grid-cols-2 gap-2 text-xs">
                <div class="bg-base-200 p-2 rounded">
                  <div class="font-semibold">🌡️ IoT & Sensors</div>
                  <div class="text-base-content/70">
                    temperature, humidity, battery_level, device_id, etc.
                  </div>
                </div>
                <div class="bg-base-200 p-2 rounded">
                  <div class="font-semibold">👤 Person Data</div>
                  <div class="text-base-content/70">name, email, username, title, etc.</div>
                </div>
                <div class="bg-base-200 p-2 rounded">
                  <div class="font-semibold">🏠 Address & Location</div>
                  <div class="text-base-content/70">city, country, address, latitude, etc.</div>
                </div>
                <div class="bg-base-200 p-2 rounded">
                  <div class="font-semibold">🏢 Company & Business</div>
                  <div class="text-base-content/70">company, buzzword, department, etc.</div>
                </div>
                <div class="bg-base-200 p-2 rounded">
                  <div class="font-semibold">🌐 Internet & Tech</div>
                  <div class="text-base-content/70">ipv4, ipv6, domain, url, etc.</div>
                </div>
                <div class="bg-base-200 p-2 rounded">
                  <div class="font-semibold">📦 Commerce & Products</div>
                  <div class="text-base-content/70">product_name, price, vehicle, dish, etc.</div>
                </div>
              </div>
            </div>
          </div>
          
    <!-- Current Payload Display -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title text-lg">📄 Current Payload</h3>
              <div class="bg-base-200 p-3 rounded text-sm font-mono">
                <%= if @payload != "" do %>
                  <pre class="whitespace-pre-wrap"><%= @payload %></pre>
                <% else %>
                  <span class="text-base-content/50 italic">No payload entered yet...</span>
                <% end %>
              </div>
              <div class="text-xs text-base-content/70 mt-2">
                Format: <span class="badge badge-sm">{@payload_format}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_info({:payload_editor_changed, payload, format}, socket) do
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:payload_format, format)

    {:noreply, socket}
  end
end
