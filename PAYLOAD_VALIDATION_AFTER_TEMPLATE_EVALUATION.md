# Payload Validation After Template Evaluation

## 概述

修改了MQTT消息发送和调度消息的payload验证逻辑，确保验证在模板求值后进行，而不是在模板求值前。这样可以正确验证最终生成的payload格式，而不是验证包含模板语法的原始字符串。

## 修改内容

### 1. 发送消息组件 (`send_message_modal_component.ex`)

#### 修改的函数：
- `handle_event("send_message", ...)` - 发送消息事件处理

#### 主要变更：
```elixir
# 之前：先验证，后求值
validated_form = validate_payload_in_form(updated_form)
{final_payload, payload_error} = process_payload(payload)

# 之后：先求值，后验证
{final_payload, payload_error} = process_payload(payload)
validated_form = validate_payload_in_form_after_template(updated_form, final_payload, payload_format)
```

#### 新增函数：
- `validate_payload_in_form_after_template/3` - 在模板求值后验证payload格式
- `extract_payload_format/2` - 正确提取payload格式信息

#### 错误处理更新：
- 添加了对模板错误的处理：`payload_error != nil`

### 2. 调度消息组件 (`scheduled_message_modal_component.ex`)

#### 修改的函数：
- `handle_event("save_scheduled_message", ...)` - 保存调度消息事件处理

#### 主要变更：
```elixir
# 之前：只验证原始模板
validated_form = validate_payload_in_form(%{"payload" => payload, "payload_format" => payload_format})

# 之后：先求值，后验证
{final_payload, payload_error} = process_payload(payload)
validated_form = validate_payload_in_form_after_template(%{"payload" => payload, "payload_format" => payload_format}, final_payload, payload_format)
```

#### 新增函数：
- `validate_payload_in_form_after_template/3` - 在模板求值后验证payload格式
- `process_payload/1` - 处理模板求值
- `has_template_syntax?/1` - 检查是否包含模板语法
- `extract_payload_format/2` - 正确提取payload格式信息

#### 错误处理更新：
- 添加了对模板错误的处理：`payload_error != nil`

## 验证逻辑

### 实时验证（用户输入时）
- 使用 `validate_payload_in_form/1` 函数
- 验证原始模板语法
- 在 `form_changed` 事件中触发
- 目的：提供实时的语法反馈

### 最终验证（发送/保存时）
- 使用 `validate_payload_in_form_after_template/3` 函数
- 先进行模板求值，然后验证求值后的结果
- 在 `send_message` 和 `save_scheduled_message` 事件中触发
- 目的：确保最终payload格式正确

## 测试场景

### 1. 有效的JSON模板
```
模板: {"temperature": {{ temperature }}, "device": "{{ device_id }}"}
求值后: {"temperature": 21.4, "device": "device_75f9e09584278c09"}
验证结果: 通过
```

### 2. 无效的JSON模板
```
模板: {"temperature": {{ temperature }}, "message": {{ sentence }}}
求值后: {"temperature": 22.4, "message": Facilis non soluta veniam iusto earum harum possimus occaecati fugit.}
验证结果: 失败 - "Invalid JSON format"
```

### 3. 有效的十六进制模板
```
模板: {{ random_hex }}
求值后: f7990d31
验证结果: 通过
```

### 4. 无效的十六进制模板
```
模板: {{ random_hex }}{{ word }}
求值后: 0c10e097dicta
验证结果: 失败 - "Invalid hex format. Use only 0-9, A-F characters"
```

## 修复的问题

### 格式字段提取问题
在初始实现中发现了一个关键问题：表单提交时的格式信息存储在不同的字段中：
- 发送消息组件：`format-main-X` (如 `format-main-4`)
- 调度消息组件：`format-scheduled-X` (如 `format-scheduled-5`)
- 但代码尝试从 `payload_format` 字段提取，导致默认使用 `"text"` 格式

### 解决方案
添加了 `extract_payload_format/2` 函数来正确提取格式信息：
1. 首先尝试从 `payload_format` 字段获取（组件状态）
2. 如果没有，则从相应的radio按钮字段提取（`format-main-X` 或 `format-scheduled-X`）
3. 最后回退到表单状态或默认值 `"text"`

## 影响

### 正面影响
1. **准确验证**：现在验证的是实际发送的payload，而不是模板
2. **更好的错误提示**：用户可以看到模板求值后的实际错误
3. **一致性**：发送消息和调度消息使用相同的验证逻辑
4. **正确的格式检测**：修复了格式字段提取问题，确保使用正确的验证规则

### 保持不变
1. **实时反馈**：用户输入时仍然有实时的语法验证
2. **向后兼容**：不影响现有的非模板payload
3. **性能**：只在发送/保存时进行额外的模板求值

## 相关文件

- `lib/mqttable_web/live/components/send_message_modal_component.ex`
- `lib/mqttable_web/live/components/scheduled_message_modal_component.ex`
- `lib/mqttable/templating/engine.ex`
- `lib/mqttable/mqtt_client/worker.ex` (调度消息执行时的模板处理)
