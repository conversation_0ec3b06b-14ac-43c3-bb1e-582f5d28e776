# Faker Library Integration for MQTT Template System

## Overview

This document describes the integration of the Faker Elixir library into the MQTT template system, providing comprehensive fake data generation capabilities for IoT and MQTT scenarios.

## Added Functions

### Person Data (6 functions)
- `fake_name` - Full name
- `fake_first_name` - First name only
- `fake_last_name` - Last name only
- `fake_title` - Professional title
- `fake_prefix` - Name prefix (Mr., Mrs., etc.)
- `fake_suffix` - Name suffix (Jr., III, etc.)

### Address Data (9 functions)
- `fake_address` - Street address
- `fake_city` - City name
- `fake_country` - Country name
- `fake_country_code` - Country code (ISO)
- `fake_state` - State/province name
- `fake_postcode` - Postal/ZIP code
- `fake_latitude` - Geographic latitude
- `fake_longitude` - Geographic longitude
- `fake_timezone` - Timezone identifier

### Company Data (4 functions)
- `fake_company` - Company name
- `fake_company_suffix` - Company suffix (Inc, LLC, etc.)
- `fake_buzzword` - Business buzzword
- `fake_catch_phrase` - Marketing catch phrase

### Internet Data (7 functions)
- `fake_email` - Email address
- `fake_username` - Username
- `fake_domain` - Domain name
- `fake_url` - Complete URL
- `fake_ipv4` - IPv4 address
- `fake_ipv6` - IPv6 address
- `fake_mac_address_faker` - MAC address

### Text Data (4 functions)
- `fake_sentence` - Random sentence
- `fake_paragraph` - Random paragraph
- `fake_word` - Single word
- `fake_words(count)` - Multiple words (with parameter)

### Commerce Data (4 functions)
- `fake_product_name` - Product name
- `fake_price` - Product price
- `fake_color_name` - Color name
- `fake_department` - Store department

### File Data (3 functions)
- `fake_filename` - File name
- `fake_file_extension` - File extension
- `fake_mime_type` - MIME type

### Food Data (3 functions)
- `fake_dish` - Dish name
- `fake_ingredient` - Food ingredient
- `fake_spice` - Spice name

### Vehicle Data (4 functions)
- `fake_vehicle` - Make and model
- `fake_vehicle_make` - Vehicle make
- `fake_vehicle_model` - Vehicle model
- `fake_vin` - Vehicle identification number

### Financial Data (4 functions)
- `fake_currency_code` - Currency code (USD, EUR, etc.)
- `fake_currency_symbol` - Currency symbol
- `fake_iban` - International bank account number
- `fake_isbn` - Book ISBN

### Date Data (3 functions)
- `fake_date_of_birth` - Birth date
- `fake_past_date(days)` - Past date (with parameter)
- `fake_future_date(days)` - Future date (with parameter)

### Avatar and Images (2 functions)
- `fake_avatar_url` - Avatar image URL
- `fake_image_url` - Generic image URL

### Blockchain Data (2 functions)
- `fake_bitcoin_address` - Bitcoin wallet address
- `fake_ethereum_address` - Ethereum wallet address

## Usage Examples

### Simple Variable Access
```liquid
Hello {{ fake_name }}, your email is {{ fake_email }}
```

### Filter Usage (for functions with parameters)
```liquid
Random words: {{ "" | fake_words: 5 }}
Past date: {{ "" | fake_past_date: 30 }}
```

### Complete MQTT Message Template
```json
{
  "timestamp": "{{ iso8601 }}",
  "device": {
    "id": "{{ device_id }}",
    "ip": "{{ fake_ipv4 }}",
    "location": {
      "city": "{{ fake_city }}",
      "country": "{{ fake_country }}"
    }
  },
  "user": {
    "name": "{{ fake_name }}",
    "email": "{{ fake_email }}",
    "company": "{{ fake_company }}"
  },
  "sensors": {
    "temperature": {{ temperature }},
    "humidity": {{ humidity }}
  }
}
```

## Technical Implementation

### Files Modified
1. `lib/mqttable/templating/functions.ex` - Added 55 new Faker functions
2. `lib/mqttable/templating/engine.ex` - Updated to include Faker functions in context

### Key Features
- **Automatic Faker startup** - Functions ensure Faker application is started
- **Error handling** - Graceful fallbacks for missing functions
- **Type consistency** - All functions return appropriate data types
- **Template integration** - Works seamlessly with existing template system

### Performance Considerations
- Functions are pre-computed in `add_function_values()` for commonly used ones
- Faker application is started only when needed
- Functions with parameters use filter syntax for dynamic values

## Benefits

1. **Realistic Test Data** - Generate believable fake data for testing
2. **Comprehensive Coverage** - 55+ functions covering various data types
3. **IoT Integration** - Perfect for MQTT and IoT device simulation
4. **Easy to Use** - Simple template syntax for all functions
5. **Extensible** - Easy to add more Faker functions as needed

## Total Function Count
- **Original functions**: 50
- **New Faker functions**: 55
- **Total available functions**: 105

This integration significantly enhances the template system's capability to generate realistic, diverse test data for MQTT and IoT applications.
